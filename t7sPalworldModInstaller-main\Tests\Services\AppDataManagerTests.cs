using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using NUnit.Framework;
using FluentAssertions;
using ModInstallerApp.Services;
using ModInstallerApp.Models;

namespace ModInstallerApp.Tests.Services
{
    /// <summary>
    /// Comprehensive unit tests for AppDataManager service
    /// </summary>
    [TestFixture]
    [Category(TestCategories.Unit)]
    public class AppDataManagerTests : TestBase
    {
        private AppDataManager? _appDataManager;
        private string _testAppDataPath = string.Empty;

        [SetUp]
        public override async Task SetUp()
        {
            await base.SetUp();

            _testAppDataPath = Path.Combine(TestDataDirectory, "test-appdata");
            Directory.CreateDirectory(_testAppDataPath);
            _appDataManager = AppDataManager.Instance;
        }

        [TearDown]
        public override async Task TearDown()
        {
            _appDataManager?.Dispose();
            await base.TearDown();
        }

        [Test]
        public void Settings_InitialCall_ReturnsDefaultSettings()
        {
            // Act
            var settings = _appDataManager!.Settings;

            // Assert
            settings.Should().NotBeNull();
            settings.RecentInstallations.Should().BeEmpty();
        }

        [Test]
        public void SaveSettings_PersistsSettings()
        {
            // Arrange
            _appDataManager!.SetLastPalworldPath("C:\\Test\\Palworld1");

            // Act
            _appDataManager.SaveSettings();

            // Assert
            var retrievedPath = _appDataManager.GetLastPalworldPath();
            retrievedPath.Should().Be("C:\\Test\\Palworld1");
        }

        [Test]
        public void AddRecentInstallation_WithNewPath_AddsToRecentList()
        {
            // Arrange
            const string newPath = "C:\\Test\\NewPalworld\\Palworld.exe";

            // Act
            _appDataManager!.AddRecentInstallation(newPath);

            // Assert
            var settings = _appDataManager.Settings;
            settings.RecentInstallations.Should().Contain(i => i.Path == newPath);
        }

        [Test]
        public void AddRecentInstallation_WithExistingPath_MovesToTop()
        {
            // Arrange
            const string existingPath = "C:\\Test\\ExistingPalworld";
            const string newPath = "C:\\Test\\NewPalworld";
            
            _appDataManager!.AddRecentInstallation(existingPath);
            _appDataManager.AddRecentInstallation(newPath);

            // Act
            _appDataManager.AddRecentInstallation(existingPath);

            // Assert
            var settings = _appDataManager.GetSettings();
            settings.RecentInstallations.First().Should().Be(existingPath);
            settings.RecentInstallations.Should().HaveCount(2);
        }

        [Test]
        public void AddRecentInstallation_ExceedsMaxCount_RemovesOldest()
        {
            // Arrange
            const int maxRecentInstallations = 10; // Assuming this is the limit
            
            // Add more than the maximum
            for (int i = 0; i < maxRecentInstallations + 2; i++)
            {
                _appDataManager!.AddRecentInstallation($"C:\\Test\\Palworld{i}");
            }

            // Assert
            var settings = _appDataManager!.GetSettings();
            settings.RecentInstallations.Should().HaveCount(maxRecentInstallations);
            settings.RecentInstallations.Should().NotContain("C:\\Test\\Palworld0");
            settings.RecentInstallations.Should().NotContain("C:\\Test\\Palworld1");
        }

        [Test]
        public void AutoDetectPalworldInstallations_FindsValidInstallations()
        {
            // Arrange
            var testInstallPath = Path.Combine(TestDataDirectory, "TestPalworld");
            var binariesPath = Path.Combine(testInstallPath, "Pal", "Binaries", "Win64");
            Directory.CreateDirectory(binariesPath);
            File.WriteAllText(Path.Combine(binariesPath, "Palworld-Win64-Shipping.exe"), "test");

            // Act
            var detectedInstallations = _appDataManager!.AutoDetectPalworldInstallations();

            // Assert
            // Note: This test may not find the test installation since AutoDetect typically
            // searches standard Steam/Epic locations. The test verifies the method doesn't crash.
            detectedInstallations.Should().NotBeNull();
        }

        [Test]
        public void RemoveRecentInstallation_WithExistingPath_RemovesFromList()
        {
            // Arrange
            const string pathToRemove = "C:\\Test\\ToRemove";
            const string pathToKeep = "C:\\Test\\ToKeep";
            
            _appDataManager!.AddRecentInstallation(pathToRemove);
            _appDataManager.AddRecentInstallation(pathToKeep);

            // Act
            _appDataManager.RemoveRecentInstallation(pathToRemove);

            // Assert
            var settings = _appDataManager.GetSettings();
            settings.RecentInstallations.Should().NotContain(pathToRemove);
            settings.RecentInstallations.Should().Contain(pathToKeep);
        }

        [Test]
        public void SaveSettings_WithNullSettings_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => _appDataManager!.SaveSettings(null!));
        }

        [Test]
        public void AddRecentInstallation_WithNullPath_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => _appDataManager!.AddRecentInstallation(null!));
        }

        [Test]
        public void AddRecentInstallation_WithEmptyPath_ThrowsArgumentException()
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => _appDataManager!.AddRecentInstallation(""));
            Assert.Throws<ArgumentException>(() => _appDataManager!.AddRecentInstallation("   "));
        }

        [Test]
        public void Constructor_WithInvalidPath_ThrowsArgumentException()
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => new AppDataManager(""));
            Assert.Throws<ArgumentException>(() => new AppDataManager("   "));
        }

        [Test]
        public void Constructor_WithNullPath_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new AppDataManager(null!));
        }

        [Test]
        public void SettingsPersistence_AcrossInstances_MaintainsData()
        {
            // Arrange
            var originalSettings = new ApplicationSettings
            {
                RecentInstallations = new[] { "C:\\Test\\Persistent" }.ToList(),
                LastUsedPath = "C:\\Test\\Persistent"
            };
            
            _appDataManager!.SaveSettings(originalSettings);
            _appDataManager.Dispose();

            // Act
            var newAppDataManager = new AppDataManager(_testAppDataPath);
            var retrievedSettings = newAppDataManager.GetSettings();

            // Assert
            retrievedSettings.RecentInstallations.Should().BeEquivalentTo(originalSettings.RecentInstallations);
            retrievedSettings.LastUsedPath.Should().Be(originalSettings.LastUsedPath);
            
            newAppDataManager.Dispose();
        }

        [Test]
        public void Dispose_DisposesResourcesProperly()
        {
            // Arrange
            var appDataManager = new AppDataManager(_testAppDataPath);
            appDataManager.AddRecentInstallation("C:\\Test\\Dispose");

            // Act
            appDataManager.Dispose();

            // Assert
            // Verify that subsequent operations throw ObjectDisposedException
            Assert.Throws<ObjectDisposedException>(() => appDataManager.GetSettings());
            Assert.Throws<ObjectDisposedException>(() => appDataManager.SaveSettings(new ApplicationSettings()));
        }
    }
}
