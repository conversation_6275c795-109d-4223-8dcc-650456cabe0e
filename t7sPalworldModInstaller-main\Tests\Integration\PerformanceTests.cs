using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using NUnit.Framework;
using FluentAssertions;
using ModInstallerApp.Services;
using ModInstallerApp.Models;

namespace ModInstallerApp.Tests.Integration
{
    /// <summary>
    /// Performance and load testing for the mod installer
    /// Tests system behavior under various load conditions
    /// </summary>
    [TestFixture]
    [Category(TestCategories.Integration)]
    [Category(TestCategories.Performance)]
    public class PerformanceTests : TestBase
    {
        private UE4SSDetector? _detector;
        private EnhancedInstallationEngine? _installationEngine;
        private ModManagerService? _modManager;

        [SetUp]
        public override async Task SetUp()
        {
            await base.SetUp();
            
            _detector = new UE4SSDetector(TestPalworldRoot, TestCacheManager!);
            _installationEngine = new EnhancedInstallationEngine(TestPalworldRoot, _detector, TestLogger!);
            _modManager = new ModManagerService(TestPalworldRoot, _installationEngine, TestLogger!);
        }

        [TearDown]
        public override async Task TearDown()
        {
            _modManager?.Dispose();
            _installationEngine?.Dispose();
            _detector?.Dispose();
            await base.TearDown();
        }

        [Test]
        public async Task UE4SSDetection_PerformanceWithLargeModCollection()
        {
            // Arrange - Create 100 test mods
            const int modCount = 100;
            await CreateLargeModCollectionAsync(modCount);
            
            // Act & Measure
            var stopwatch = Stopwatch.StartNew();
            var result = await _detector!.DetectUE4SSAsync();
            stopwatch.Stop();
            
            // Assert
            result.Should().NotBeNull();
            result.UserModsDetected.Should().HaveCount(modCount);
            
            // Performance assertion - should complete within 5 seconds
            stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromSeconds(5));
            
            TestContext.WriteLine($"Detection of {modCount} mods completed in {stopwatch.ElapsedMilliseconds}ms");
            TestContext.WriteLine($"Average time per mod: {stopwatch.ElapsedMilliseconds / (double)modCount:F2}ms");
        }

        [Test]
        public async Task ConcurrentModInstallation_PerformanceTest()
        {
            // Arrange
            const int concurrentInstalls = 10;
            var modArchives = new List<string>();
            
            for (int i = 0; i < concurrentInstalls; i++)
            {
                var archive = await CreateTestModArchiveAsync($"ConcurrentMod{i:D2}", ModStructureType.UE4SS);
                modArchives.Add(archive);
            }
            
            // Act & Measure
            var stopwatch = Stopwatch.StartNew();
            
            var installTasks = modArchives.Select(async (archive, index) =>
            {
                var progress = new Progress<InstallationProgress>();
                return await _installationEngine!.InstallModAsync(archive, progress, CancellationToken.None);
            });
            
            var results = await Task.WhenAll(installTasks);
            stopwatch.Stop();
            
            // Assert
            results.Should().HaveCount(concurrentInstalls);
            results.Should().AllSatisfy(r => r.Should().NotBeNull());
            
            // Performance assertion
            stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromMinutes(2));
            
            TestContext.WriteLine($"Concurrent installation of {concurrentInstalls} mods completed in {stopwatch.ElapsedMilliseconds}ms");
            TestContext.WriteLine($"Average time per mod: {stopwatch.ElapsedMilliseconds / (double)concurrentInstalls:F2}ms");
        }

        [Test]
        public async Task LargeArchiveInstallation_PerformanceTest()
        {
            // Arrange - Create a large test archive (simulated)
            var largeArchive = await CreateLargeTestArchiveAsync("LargeMod", 50); // 50 files
            
            var progressReports = new List<InstallationProgress>();
            var progress = new Progress<InstallationProgress>(p => progressReports.Add(p));
            
            // Act & Measure
            var stopwatch = Stopwatch.StartNew();
            var result = await _installationEngine!.InstallModAsync(largeArchive, progress, CancellationToken.None);
            stopwatch.Stop();
            
            // Assert
            result.Should().NotBeNull();
            result.InstalledFiles.Should().HaveCountGreaterOrEqualTo(50);
            
            // Progress should be reported regularly
            progressReports.Should().NotBeEmpty();
            progressReports.Should().Contain(p => p.PercentComplete == 100);
            
            // Performance assertion
            stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromMinutes(1));
            
            TestContext.WriteLine($"Large archive installation completed in {stopwatch.ElapsedMilliseconds}ms");
            TestContext.WriteLine($"Progress reports: {progressReports.Count}");
        }

        [Test]
        public async Task ModManagerService_LoadPerformanceTest()
        {
            // Arrange - Install multiple mods first
            const int modCount = 50;
            for (int i = 0; i < modCount; i++)
            {
                var archive = await CreateTestModArchiveAsync($"LoadTestMod{i:D3}", ModStructureType.UE4SS);
                await _installationEngine!.InstallModAsync(archive, null, CancellationToken.None);
            }
            
            // Act & Measure
            var stopwatch = Stopwatch.StartNew();
            var allMods = await _modManager!.GetAllModsAsync();
            stopwatch.Stop();
            
            // Assert
            allMods.Should().HaveCountGreaterOrEqualTo(modCount);
            
            // Performance assertion
            stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromSeconds(3));
            
            TestContext.WriteLine($"Loading {allMods.Count} mods completed in {stopwatch.ElapsedMilliseconds}ms");
        }

        [Test]
        public async Task CachePerformance_HitRatioTest()
        {
            // Arrange
            TestCacheManager!.ClearAll();
            const int iterations = 20;
            
            // Act & Measure
            var times = new List<long>();
            
            for (int i = 0; i < iterations; i++)
            {
                var stopwatch = Stopwatch.StartNew();
                await _detector!.DetectUE4SSAsync();
                stopwatch.Stop();
                times.Add(stopwatch.ElapsedMilliseconds);
            }
            
            // Assert
            // First call should be slower (cache miss)
            times[0].Should().BeGreaterThan(times[1]);
            
            // Subsequent calls should be consistently fast (cache hits)
            var averageAfterFirst = times.Skip(1).Average();
            var firstCallTime = times[0];
            
            averageAfterFirst.Should().BeLessThan(firstCallTime * 0.5); // At least 50% faster
            
            TestContext.WriteLine($"First call (cache miss): {firstCallTime}ms");
            TestContext.WriteLine($"Average subsequent calls (cache hits): {averageAfterFirst:F2}ms");
            TestContext.WriteLine($"Cache performance improvement: {(firstCallTime / averageAfterFirst):F1}x");
        }

        [Test]
        public async Task MemoryUsage_StabilityTest()
        {
            // Arrange
            const int iterations = 100;
            var initialMemory = GC.GetTotalMemory(true);
            
            // Act - Perform many operations
            for (int i = 0; i < iterations; i++)
            {
                var archive = await CreateTestModArchiveAsync($"MemTestMod{i:D3}", ModStructureType.UE4SS);
                var result = await _installationEngine!.InstallModAsync(archive, null, CancellationToken.None);
                
                // Rollback to prevent disk space issues
                await _installationEngine!.RollbackInstallationAsync(result.Operation!.Id, CancellationToken.None);
                
                // Force garbage collection every 10 iterations
                if (i % 10 == 0)
                {
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                }
            }
            
            // Final garbage collection
            GC.Collect();
            GC.WaitForPendingFinalizers();
            var finalMemory = GC.GetTotalMemory(true);
            
            // Assert
            var memoryIncrease = finalMemory - initialMemory;
            var memoryIncreasePercent = (memoryIncrease * 100.0) / initialMemory;
            
            // Memory increase should be reasonable (less than 50% increase)
            memoryIncreasePercent.Should().BeLessThan(50);
            
            TestContext.WriteLine($"Initial memory: {initialMemory / 1024 / 1024:F2} MB");
            TestContext.WriteLine($"Final memory: {finalMemory / 1024 / 1024:F2} MB");
            TestContext.WriteLine($"Memory increase: {memoryIncrease / 1024 / 1024:F2} MB ({memoryIncreasePercent:F1}%)");
        }

        [Test]
        public async Task DiskIO_PerformanceTest()
        {
            // Arrange
            var testData = TestUtilities.GenerateTestData(10 * 1024 * 1024); // 10MB
            var testFile = Path.Combine(TestDataDirectory, "performance_test.dat");
            
            // Act & Measure Write Performance
            var writeStopwatch = Stopwatch.StartNew();
            await File.WriteAllBytesAsync(testFile, testData);
            writeStopwatch.Stop();
            
            // Act & Measure Read Performance
            var readStopwatch = Stopwatch.StartNew();
            var readData = await File.ReadAllBytesAsync(testFile);
            readStopwatch.Stop();
            
            // Assert
            readData.Should().BeEquivalentTo(testData);
            
            var writeSpeed = (testData.Length / 1024.0 / 1024.0) / (writeStopwatch.ElapsedMilliseconds / 1000.0);
            var readSpeed = (readData.Length / 1024.0 / 1024.0) / (readStopwatch.ElapsedMilliseconds / 1000.0);
            
            // Performance assertions (should achieve reasonable disk I/O speeds)
            writeSpeed.Should().BeGreaterThan(10); // At least 10 MB/s write
            readSpeed.Should().BeGreaterThan(50);  // At least 50 MB/s read
            
            TestContext.WriteLine($"Write speed: {writeSpeed:F2} MB/s");
            TestContext.WriteLine($"Read speed: {readSpeed:F2} MB/s");
        }

        [Test]
        public async Task SearchPerformance_LargeModCollection()
        {
            // Arrange - Create mods with various names for searching
            const int modCount = 200;
            var modNames = new List<string>();
            
            for (int i = 0; i < modCount; i++)
            {
                var name = $"SearchTestMod{i:D3}_{GenerateRandomModName()}";
                modNames.Add(name);
                await CreateTestModArchiveAsync(name, ModStructureType.UE4SS);
            }
            
            // Install all mods
            foreach (var name in modNames)
            {
                var archive = Path.Combine(TestModsDirectory, $"{name}.zip");
                await _installationEngine!.InstallModAsync(archive, null, CancellationToken.None);
            }
            
            // Act & Measure Search Performance
            var searchTerms = new[] { "Weapon", "Armor", "Utility", "Building", "Character" };
            var searchTimes = new List<long>();
            
            foreach (var term in searchTerms)
            {
                var stopwatch = Stopwatch.StartNew();
                var searchResults = await _modManager!.SearchModsAsync(term);
                stopwatch.Stop();
                
                searchTimes.Add(stopwatch.ElapsedMilliseconds);
                
                TestContext.WriteLine($"Search for '{term}': {searchResults.Count} results in {stopwatch.ElapsedMilliseconds}ms");
            }
            
            // Assert
            var averageSearchTime = searchTimes.Average();
            averageSearchTime.Should().BeLessThan(500); // Should complete within 500ms
            
            TestContext.WriteLine($"Average search time across {modCount} mods: {averageSearchTime:F2}ms");
        }

        private async Task CreateLargeModCollectionAsync(int count)
        {
            var modsPath = Path.Combine(TestPalworldRoot, "Pal", "Binaries", "Win64", "ue4ss", "Mods");
            
            for (int i = 0; i < count; i++)
            {
                var modPath = Path.Combine(modsPath, $"PerfTestMod{i:D3}");
                Directory.CreateDirectory(modPath);
                
                await File.WriteAllTextAsync(Path.Combine(modPath, "enabled.txt"), "");
                await File.WriteAllTextAsync(Path.Combine(modPath, "main.lua"), $"-- Performance test mod {i}");
            }
        }

        private async Task<string> CreateLargeTestArchiveAsync(string modName, int fileCount)
        {
            var archivePath = Path.Combine(TestModsDirectory, $"{modName}.zip");
            var tempModDir = Path.Combine(TestModsDirectory, $"temp_{modName}");
            
            try
            {
                Directory.CreateDirectory(tempModDir);
                
                // Create multiple files to simulate a large mod
                for (int i = 0; i < fileCount; i++)
                {
                    var fileName = $"file_{i:D3}.txt";
                    var filePath = Path.Combine(tempModDir, fileName);
                    var content = $"Large mod file {i} content - " + new string('x', 1000); // 1KB per file
                    await File.WriteAllTextAsync(filePath, content);
                }
                
                // Create main mod files
                await File.WriteAllTextAsync(Path.Combine(tempModDir, "enabled.txt"), "");
                await File.WriteAllTextAsync(Path.Combine(tempModDir, "main.lua"), $"-- Large mod {modName}");
                
                // Create mock ZIP (in real implementation would use proper ZIP library)
                await File.WriteAllTextAsync(archivePath, $"Mock large ZIP archive for {modName}");
                
                return archivePath;
            }
            finally
            {
                if (Directory.Exists(tempModDir))
                {
                    Directory.Delete(tempModDir, true);
                }
            }
        }

        private string GenerateRandomModName()
        {
            var categories = new[] { "Weapon", "Armor", "Utility", "Building", "Character", "Environment" };
            var adjectives = new[] { "Enhanced", "Improved", "Advanced", "Ultimate", "Epic", "Legendary" };
            var random = new Random();
            
            var category = categories[random.Next(categories.Length)];
            var adjective = adjectives[random.Next(adjectives.Length)];
            
            return $"{adjective}{category}";
        }
    }
}
